
* {
    margin: 0;
    padding: 0;
    line-height: 1.3;
    font-family: sans-serif;
    color: #333542;
}



html, body {
    height: 100%;
    margin: 0;
    padding: 0;
}

.footer {
    position: absolute;
    bottom: 0;
    width: 100%;
}


body {
    font-size: .75rem;
}

img {
    max-width: 100%;
}

.customers {
    font-family: Arial, Helvetica, sans-serif;
    border-collapse: collapse;
    width: 100%;
}
table {
    width: 100%;
}

table thead th {
    padding: 8px;
    font-size: 11px;
    text-align: start;
}

table tbody th,
table tbody td {
    padding: 8px;
    font-size: 11px;
}

table.fz-12 thead th {
    font-size: 12px;
}

table.fz-12 tbody th,
table.fz-12 tbody td {
    font-size: 12px;
}

table.customers thead th {
    background-color: #0177CD;
    color: #fff;
}

table.customers tbody th,
table.customers tbody td {
    background-color: #FAFCFF;
}

table.calc-table th {
    text-align: start;
}

table.calc-table td {
    text-align: end;
}
table.calc-table td.text-left {
    text-align: start;
}

.table-total {
    font-family: Arial, Helvetica, sans-serif;
}


.text-left {
    text-align: start !important;
}

.pb-2 {
    padding-bottom: 8px !important;
}

.pb-3 {
    padding-bottom: 16px !important;
}

.text-right {
    text-align: end;
}

.content-position {
    padding: 15px 40px;
}

.content-position-y {
    padding: 0px 40px;
}

.text-white {
    color: white !important;
}

.bs-0 {
    border-spacing: 0;
}
.text-center {
    text-align: center;
}
.mb-1 {
    margin-bottom: 4px !important;
}
.mb-2 {
    margin-bottom: 8px !important;
}
.mb-4 {
    margin-bottom: 24px !important;
}
.mb-30 {
    margin-bottom: 30px !important;
}
.px-10 {
    padding-inline-start: 10px;
    padding-inline-end: 10px;
}
.fz-14 {
    font-size: 14px;
}
.fz-12 {
    font-size: 12px;
}
.fz-10 {
    font-size: 10px;
}
.font-normal {
    font-weight: 400;
}
.border-dashed-top {
    border-top: 1px dashed #ddd;
}
.font-weight-bold {
    font-weight: 700;
}
.bg-light {
    background-color: #F7F7F7;
}
.py-30 {
    padding-top: 30px;
    padding-bottom: 30px;
}
.py-4 {
    padding-top: 24px;
    padding-bottom: 24px;
}
.d-flex {
    display: flex;
}
.gap-2 {
    gap: 8px;
}
.flex-wrap {
    flex-wrap: wrap;
}
.align-items-center {
    align-items: center;
}
.justify-content-center {
    justify-content: center;
}
a {
    color: rgba(0, 128, 245, 1);
}
.p-1 {
    padding: 4px !important;
}
.h2 {
    font-size: 1.5em;
    margin-block-start: 0.83em;
    margin-block-end: 0.83em;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    font-weight: bold;
}

.h4 {
    margin-block-start: 1.33em;
    margin-block-end: 1.33em;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    font-weight: bold;
}

.custom-class-1{
    margin-top: 6px;
    margin-bottom: 0px;
}
.custom-class-background{
    background-color: #107980 important;
}
.custom-class-background-transparent{
    background-color: transparent !important; color: #333542"
}
