<?php
echo "Starting server restart and cache clearing process...\n";

// Clear OPcache if available
if (function_exists('opcache_reset')) {
    opcache_reset();
    echo "✓ OPcache cleared\n";
} else {
    echo "- OPcache not available\n";
}

// Clear APCu cache if available
if (function_exists('apcu_clear_cache')) {
    apcu_clear_cache();
    echo "✓ APCu cache cleared\n";
} else {
    echo "- APCu cache not available\n";
}

// Clear Laravel caches
$cacheFiles = [
    'bootstrap/cache/config.php' => 'Config cache',
    'bootstrap/cache/routes.php' => 'Routes cache',
    'bootstrap/cache/services.php' => 'Services cache',
    'bootstrap/cache/packages.php' => 'Packages cache'
];

foreach ($cacheFiles as $file => $description) {
    if (file_exists($file)) {
        unlink($file);
        echo "✓ $description cleared\n";
    } else {
        echo "- $description not found\n";
    }
}

// Clear storage cache directories
$cacheDirs = [
    'storage/framework/cache/data',
    'storage/framework/sessions',
    'storage/framework/views'
];

foreach ($cacheDirs as $dir) {
    if (is_dir($dir)) {
        $files = glob($dir . '/*');
        $count = 0;
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
                $count++;
            }
        }
        echo "✓ Cleared $count files from $dir\n";
    }
}

// Test the BusinessSetting model fix
echo "\nTesting BusinessSetting model...\n";
try {
    // Include Laravel autoloader
    require_once 'vendor/autoload.php';
    
    // Bootstrap Laravel
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    
    // Test creating a BusinessSetting record
    $setting = new App\Models\BusinessSetting();
    $setting->key = 'test_key_' . time();
    $setting->value = 'test_value';
    
    // This should not throw an error anymore
    $setting->save();
    echo "✓ BusinessSetting model test passed - no generateSlug error!\n";
    
    // Clean up test record
    $setting->delete();
    echo "✓ Test record cleaned up\n";
    
} catch (Exception $e) {
    echo "✗ BusinessSetting model test failed: " . $e->getMessage() . "\n";
    echo "Error on line: " . $e->getLine() . "\n";
}

echo "\nCache clearing completed!\n";
echo "Please restart your web server (Apache/Nginx) to ensure all changes take effect.\n";
echo "You can also try accessing your application now.\n";
?>
