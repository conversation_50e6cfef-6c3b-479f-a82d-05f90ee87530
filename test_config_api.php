<?php
echo "=== TESTING CONFIG API ENDPOINT ===\n\n";

try {
    // Include Laravel autoloader
    require_once 'vendor/autoload.php';
    
    // Bootstrap Laravel
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    
    echo "✓ Laravel application bootstrapped\n\n";
    
    // Test database connection
    echo "Testing database connection...\n";
    $pdo = new PDO("mysql:host=localhost;dbname=turantadmin", 'turantadmin', 'a75L2cytGNeamnXj');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✓ Database connection successful\n\n";
    
    // Test the specific queries that were failing
    echo "Testing problematic queries...\n";
    
    $queries = [
        "SELECT MIN(day_wise_price) as aggregate FROM vehicles WHERE day_wise_price > 0" => "day_wise_price query",
        "SELECT MIN(hourly_price) as aggregate FROM vehicles WHERE hourly_price > 0" => "hourly_price query", 
        "SELECT MIN(distance_price) as aggregate FROM vehicles WHERE distance_price > 0" => "distance_price query",
        "SELECT * FROM business_settings WHERE `key` = 'system_language' LIMIT 1" => "business_settings query"
    ];
    
    foreach ($queries as $query => $description) {
        try {
            echo "Testing $description...\n";
            $stmt = $pdo->query($query);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "✓ $description successful\n";
            if (isset($result['aggregate'])) {
                echo "  Result: " . ($result['aggregate'] ?? 'NULL') . "\n";
            }
        } catch (Exception $e) {
            echo "✗ $description failed: " . $e->getMessage() . "\n";
        }
        echo "\n";
    }
    
    // Test the Vehicle model directly
    echo "Testing Vehicle model...\n";
    try {
        // Check if the Vehicle model exists
        if (class_exists('Modules\Rental\Entities\Vehicle')) {
            $vehicleClass = 'Modules\Rental\Entities\Vehicle';
        } elseif (class_exists('App\Models\Vehicle')) {
            $vehicleClass = 'App\Models\Vehicle';
        } else {
            throw new Exception("Vehicle model not found");
        }
        
        echo "Using Vehicle model: $vehicleClass\n";
        
        // Test the specific queries that were failing in the ConfigController
        $vehicle_distance_min = $vehicleClass::where('distance_price', '>', '0')->min('distance_price');
        echo "✓ Distance price min query: " . ($vehicle_distance_min ?? 'NULL') . "\n";
        
        $vehicle_hourly_min = $vehicleClass::where('hourly_price', '>', '0')->min('hourly_price');
        echo "✓ Hourly price min query: " . ($vehicle_hourly_min ?? 'NULL') . "\n";
        
        $vehicle_day_wise_min = $vehicleClass::where('day_wise_price', '>', '0')->min('day_wise_price');
        echo "✓ Day wise price min query: " . ($vehicle_day_wise_min ?? 'NULL') . "\n";
        
    } catch (Exception $e) {
        echo "✗ Vehicle model test failed: " . $e->getMessage() . "\n";
    }
    
    echo "\n=== TESTING CONFIG CONTROLLER LOGIC ===\n";
    
    try {
        // Test the addon_published_status function
        if (function_exists('addon_published_status')) {
            $rentalStatus = addon_published_status('Rental');
            echo "Rental addon status: " . ($rentalStatus ? 'ENABLED' : 'DISABLED') . "\n";
        } else {
            echo "addon_published_status function not found\n";
        }
        
        // Test Cache facade
        if (class_exists('Illuminate\Support\Facades\Cache')) {
            echo "✓ Cache facade available\n";
            
            // Clear any existing cache for these keys
            $cacheKeys = [
                'vehicle_dis_min_price_conf',
                'vehicle_hour_min_price_conf', 
                'vehicle_day_wise_min_price_conf'
            ];
            
            foreach ($cacheKeys as $key) {
                \Illuminate\Support\Facades\Cache::forget($key);
                echo "Cleared cache key: $key\n";
            }
        }
        
    } catch (Exception $e) {
        echo "✗ Config controller test failed: " . $e->getMessage() . "\n";
    }
    
    echo "\n=== SIMULATING API REQUEST ===\n";
    
    try {
        // Create a mock request to test the config endpoint logic
        $request = new \Illuminate\Http\Request();
        
        // Test the ConfigController directly
        if (class_exists('App\Http\Controllers\Api\V1\ConfigController')) {
            echo "Testing ConfigController...\n";
            
            $controller = new \App\Http\Controllers\Api\V1\ConfigController();
            
            // This should not throw an error anymore
            $response = $controller->configuration($request);
            
            if ($response instanceof \Illuminate\Http\JsonResponse) {
                echo "✓ ConfigController->configuration() executed successfully\n";
                echo "Response status: " . $response->getStatusCode() . "\n";
            } else {
                echo "✓ ConfigController->configuration() executed (non-JSON response)\n";
            }
            
        } else {
            echo "✗ ConfigController class not found\n";
        }
        
    } catch (Exception $e) {
        echo "✗ API simulation failed: " . $e->getMessage() . "\n";
        echo "Error on line: " . $e->getLine() . "\n";
        echo "File: " . $e->getFile() . "\n";
    }
    
    echo "\n=== TESTING HTTP REQUEST ===\n";
    
    // Test the actual HTTP endpoint
    $url = 'https://admin.turantapp.com/api/v1/config';
    echo "Testing URL: $url\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Test Script');
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Accept: application/json',
        'Content-Type: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "✗ CURL Error: $error\n";
    } else {
        echo "HTTP Status Code: $httpCode\n";
        
        switch ($httpCode) {
            case 200:
                echo "✓ SUCCESS! API endpoint is working\n";
                $data = json_decode($response, true);
                if ($data) {
                    echo "Response contains " . count($data) . " keys\n";
                } else {
                    echo "Response preview: " . substr($response, 0, 200) . "...\n";
                }
                break;
            case 405:
                echo "✗ Method Not Allowed (405) - This was the original error\n";
                break;
            case 500:
                echo "✗ Internal Server Error (500) - Check error logs\n";
                echo "Response: " . substr($response, 0, 500) . "\n";
                break;
            default:
                echo "HTTP Code: $httpCode\n";
                echo "Response: " . substr($response, 0, 200) . "...\n";
                break;
        }
    }
    
    echo "\n✓ Testing completed!\n";
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
