<?php
// Clear OPcache
if (function_exists('opcache_reset')) {
    opcache_reset();
    echo "OPcache cleared successfully.\n";
} else {
    echo "OPcache is not enabled.\n";
}

// Test database connection
try {
    $host = 'localhost';
    $dbname = 'turantadmin';
    $username = 'turantadmin';
    $password = 'a75L2cytGNeamnXj';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "Database connection successful!\n";
    
    // Test if business_settings table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'business_settings'");
    if ($stmt->rowCount() > 0) {
        echo "business_settings table exists.\n";
        
        // Test a simple query
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM business_settings");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "business_settings table has " . $result['count'] . " records.\n";
    } else {
        echo "business_settings table does NOT exist!\n";
    }
    
} catch (PDOException $e) {
    echo "Database connection failed: " . $e->getMessage() . "\n";
}

// Clear Laravel caches
echo "Clearing Laravel caches...\n";
if (file_exists('bootstrap/cache/config.php')) {
    unlink('bootstrap/cache/config.php');
    echo "Config cache cleared.\n";
}

if (file_exists('bootstrap/cache/routes.php')) {
    unlink('bootstrap/cache/routes.php');
    echo "Routes cache cleared.\n";
}

if (file_exists('bootstrap/cache/services.php')) {
    unlink('bootstrap/cache/services.php');
    echo "Services cache cleared.\n";
}

// Clear storage cache files
$cacheDir = 'storage/framework/cache/data';
if (is_dir($cacheDir)) {
    $files = glob($cacheDir . '/*');
    foreach ($files as $file) {
        if (is_file($file)) {
            unlink($file);
        }
    }
    echo "Storage cache cleared.\n";
}

echo "Cache clearing completed!\n";
?>
