<?php
echo "Checking and fixing vehicles table structure...\n\n";

try {
    $host = 'localhost';
    $dbname = 'turantadmin';
    $username = 'turantadmin';
    $password = 'a75L2cytGNeamnXj';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✓ Database connection successful!\n\n";
    
    // Check if vehicles table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'vehicles'");
    if ($stmt->rowCount() == 0) {
        echo "✗ vehicles table does not exist!\n";
        echo "Creating vehicles table...\n";
        
        // Create vehicles table with all necessary columns
        $createTableSQL = "
        CREATE TABLE `vehicles` (
            `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `type` varchar(100) DEFAULT NULL,
            `category_id` bigint(20) UNSIGNED DEFAULT NULL,
            `starting_coverage_area` double DEFAULT NULL,
            `maximum_coverage_area` double DEFAULT NULL,
            `per_km_charge` decimal(8,2) DEFAULT NULL,
            `minimum_charge` decimal(8,2) DEFAULT NULL,
            `day_wise_price` decimal(8,2) DEFAULT 0.00,
            `status` tinyint(1) DEFAULT 1,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $pdo->exec($createTableSQL);
        echo "✓ vehicles table created successfully!\n";
    } else {
        echo "✓ vehicles table exists\n";
        
        // Check current table structure
        echo "Checking table structure...\n";
        $stmt = $pdo->query("DESCRIBE vehicles");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $columnNames = array_column($columns, 'Field');
        echo "Current columns: " . implode(', ', $columnNames) . "\n\n";
        
        // Check if day_wise_price column exists
        if (!in_array('day_wise_price', $columnNames)) {
            echo "✗ day_wise_price column is missing!\n";
            echo "Adding day_wise_price column...\n";
            
            $alterSQL = "ALTER TABLE `vehicles` ADD COLUMN `day_wise_price` DECIMAL(8,2) DEFAULT 0.00 AFTER `minimum_charge`";
            $pdo->exec($alterSQL);
            echo "✓ day_wise_price column added successfully!\n";
        } else {
            echo "✓ day_wise_price column already exists\n";
        }
        
        // Check for other commonly needed columns
        $requiredColumns = [
            'per_km_charge' => 'DECIMAL(8,2) DEFAULT NULL',
            'minimum_charge' => 'DECIMAL(8,2) DEFAULT NULL',
            'status' => 'TINYINT(1) DEFAULT 1',
            'starting_coverage_area' => 'DOUBLE DEFAULT NULL',
            'maximum_coverage_area' => 'DOUBLE DEFAULT NULL'
        ];
        
        foreach ($requiredColumns as $column => $definition) {
            if (!in_array($column, $columnNames)) {
                echo "Adding missing column: $column\n";
                $alterSQL = "ALTER TABLE `vehicles` ADD COLUMN `$column` $definition";
                $pdo->exec($alterSQL);
                echo "✓ $column column added\n";
            }
        }
    }
    
    // Show final table structure
    echo "\nFinal table structure:\n";
    $stmt = $pdo->query("DESCRIBE vehicles");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($columns as $column) {
        echo "- {$column['Field']} ({$column['Type']}) {$column['Null']} {$column['Default']}\n";
    }
    
    // Test the query that was failing
    echo "\nTesting the problematic query...\n";
    try {
        $stmt = $pdo->query("SELECT MIN(day_wise_price) as aggregate FROM vehicles WHERE day_wise_price > 0");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "✓ Query executed successfully! Result: " . ($result['aggregate'] ?? 'NULL') . "\n";
    } catch (Exception $e) {
        echo "✗ Query still failing: " . $e->getMessage() . "\n";
    }
    
    echo "\n✓ Database fix completed!\n";
    
} catch (PDOException $e) {
    echo "✗ Database error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
}
?>
