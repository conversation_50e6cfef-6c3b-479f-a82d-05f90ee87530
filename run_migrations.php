<?php
echo "=== RUNNING LARAVEL MIGRATIONS ===\n\n";

try {
    // Include Laravel autoloader
    require_once 'vendor/autoload.php';
    
    // Bootstrap Laravel
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    
    echo "✓ Laravel application bootstrapped\n\n";
    
    // Check migration status
    echo "Checking migration status...\n";
    
    // Get all migration files
    $migrationPaths = [
        'database/migrations',
        'Modules/Rental/Database/Migrations'
    ];
    
    $vehicleRelatedMigrations = [];
    
    foreach ($migrationPaths as $path) {
        if (is_dir($path)) {
            $files = glob($path . '/*.php');
            foreach ($files as $file) {
                $filename = basename($file);
                if (strpos(strtolower($filename), 'vehicle') !== false) {
                    $vehicleRelatedMigrations[] = $file;
                    echo "Found vehicle migration: $filename\n";
                }
            }
        }
    }
    
    echo "\nFound " . count($vehicleRelatedMigrations) . " vehicle-related migrations\n\n";
    
    // Check which migrations have been run
    echo "Checking migration table...\n";
    
    $pdo = new PDO("mysql:host=localhost;dbname=turantadmin", 'turantadmin', 'a75L2cytGNeamnXj');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if migrations table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'migrations'");
    if ($stmt->rowCount() == 0) {
        echo "✗ Migrations table doesn't exist. Creating it...\n";
        
        $createMigrationsTable = "
        CREATE TABLE `migrations` (
            `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `migration` varchar(255) NOT NULL,
            `batch` int(11) NOT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $pdo->exec($createMigrationsTable);
        echo "✓ Migrations table created\n";
    }
    
    // Get list of completed migrations
    $stmt = $pdo->query("SELECT migration FROM migrations");
    $completedMigrations = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "Completed migrations: " . count($completedMigrations) . "\n";
    
    // Check specific vehicle migrations
    $vehicleMigrationNames = [
        '2024_12_08_155202_create_vehicles_table',
        '2025_05_12_124546_add_day_wise_charge_col_in_vehicles_table'
    ];
    
    foreach ($vehicleMigrationNames as $migrationName) {
        if (in_array($migrationName, $completedMigrations)) {
            echo "✓ $migrationName - COMPLETED\n";
        } else {
            echo "✗ $migrationName - NOT RUN\n";
        }
    }
    
    echo "\n=== RUNNING SPECIFIC VEHICLE MIGRATIONS ===\n";
    
    // Try to run the specific migrations manually
    foreach ($vehicleRelatedMigrations as $migrationFile) {
        $filename = basename($migrationFile, '.php');
        
        if (!in_array($filename, $completedMigrations)) {
            echo "Running migration: $filename\n";
            
            try {
                // Include the migration file
                require_once $migrationFile;
                
                // Extract class name from filename
                $parts = explode('_', $filename);
                $className = '';
                for ($i = 4; $i < count($parts); $i++) {
                    $className .= ucfirst($parts[$i]);
                }
                
                if (class_exists($className)) {
                    $migration = new $className();
                    
                    // Run the up method
                    $migration->up();
                    
                    // Record in migrations table
                    $stmt = $pdo->prepare("INSERT INTO migrations (migration, batch) VALUES (?, ?)");
                    $stmt->execute([$filename, 1]);
                    
                    echo "✓ $filename executed successfully\n";
                } else {
                    echo "✗ Class $className not found in $filename\n";
                }
                
            } catch (Exception $e) {
                echo "✗ Failed to run $filename: " . $e->getMessage() . "\n";
            }
        }
    }
    
    echo "\n=== TESTING VEHICLES TABLE ===\n";
    
    // Test if vehicles table exists and has the required columns
    try {
        $stmt = $pdo->query("DESCRIBE vehicles");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $columnNames = array_column($columns, 'Field');
        
        $requiredColumns = ['day_wise_price', 'trip_day_wise', 'hourly_price', 'distance_price'];
        
        foreach ($requiredColumns as $column) {
            if (in_array($column, $columnNames)) {
                echo "✓ $column column exists\n";
            } else {
                echo "✗ $column column missing\n";
            }
        }
        
        // Test the problematic query
        echo "\nTesting problematic query...\n";
        $stmt = $pdo->query("SELECT MIN(day_wise_price) as aggregate FROM vehicles WHERE day_wise_price > 0");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "✓ Query successful! Result: " . ($result['aggregate'] ?? 'NULL') . "\n";
        
    } catch (Exception $e) {
        echo "✗ Vehicles table test failed: " . $e->getMessage() . "\n";
    }
    
    echo "\n✓ Migration process completed!\n";
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
