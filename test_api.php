<?php
echo "Testing API endpoints...\n\n";

// Test the config endpoint that was mentioned as problematic
$baseUrl = 'https://admin.turantapp.com';
$endpoints = [
    '/api/v1/config',
    '/',
    '/admin'
];

foreach ($endpoints as $endpoint) {
    $url = $baseUrl . $endpoint;
    echo "Testing: $url\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Test Script');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "  ✗ CURL Error: $error\n";
    } else {
        switch ($httpCode) {
            case 200:
                echo "  ✓ Success (200 OK)\n";
                break;
            case 302:
                echo "  ✓ Redirect (302) - This is normal for some endpoints\n";
                break;
            case 405:
                echo "  ✗ Method Not Allowed (405) - This was the original error\n";
                break;
            case 500:
                echo "  ✗ Internal Server Error (500)\n";
                break;
            default:
                echo "  ? HTTP Code: $httpCode\n";
                break;
        }
    }
    
    // Show first 200 characters of response for debugging
    if ($response && strlen($response) > 0) {
        $preview = substr(strip_tags($response), 0, 200);
        echo "  Response preview: " . trim($preview) . "...\n";
    }
    
    echo "\n";
}

echo "API testing completed!\n";
?>
