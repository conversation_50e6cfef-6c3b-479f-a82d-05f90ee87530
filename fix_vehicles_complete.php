<?php
echo "=== COMPREHENSIVE VEHICLES TABLE FIX ===\n\n";

try {
    $host = 'localhost';
    $dbname = 'turantadmin';
    $username = 'turantadmin';
    $password = 'a75L2cytGNeamnXj';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✓ Database connection successful!\n\n";
    
    // Check if vehicles table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'vehicles'");
    if ($stmt->rowCount() == 0) {
        echo "✗ vehicles table does not exist!\n";
        echo "Creating vehicles table with complete structure...\n";
        
        // Create vehicles table with all necessary columns based on the rental module migration
        $createTableSQL = "
        CREATE TABLE `vehicles` (
            `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            `name` varchar(255) DEFAULT NULL,
            `description` text DEFAULT NULL,
            `thumbnail` varchar(255) DEFAULT NULL,
            `images` text DEFAULT NULL,
            `zone_id` bigint(20) UNSIGNED DEFAULT NULL,
            `provider_id` bigint(20) UNSIGNED DEFAULT NULL,
            `brand_id` bigint(20) UNSIGNED DEFAULT NULL,
            `category_id` bigint(20) UNSIGNED DEFAULT NULL,
            `model` varchar(255) DEFAULT NULL,
            `type` varchar(255) DEFAULT NULL,
            `engine_capacity` varchar(255) DEFAULT NULL,
            `engine_power` varchar(255) DEFAULT NULL,
            `seating_capacity` varchar(255) DEFAULT NULL,
            `air_condition` tinyint(1) DEFAULT 0,
            `fuel_type` varchar(255) DEFAULT NULL,
            `transmission_type` varchar(255) DEFAULT NULL,
            `multiple_vehicles` tinyint(1) DEFAULT 0,
            `trip_hourly` tinyint(1) DEFAULT 0,
            `trip_distance` tinyint(1) DEFAULT 0,
            `trip_day_wise` double DEFAULT 0,
            `hourly_price` decimal(23,8) DEFAULT 0.00000000,
            `distance_price` decimal(23,8) DEFAULT 0.00000000,
            `day_wise_price` decimal(23,8) DEFAULT 0.00000000,
            `discount_type` varchar(255) DEFAULT NULL,
            `discount_price` decimal(23,8) DEFAULT 0.00000000,
            `tag` text DEFAULT NULL,
            `documents` text DEFAULT NULL,
            `status` tinyint(1) DEFAULT 1,
            `new_tag` tinyint(1) DEFAULT 1,
            `total_trip` int(11) DEFAULT 0,
            `avg_rating` decimal(8,2) DEFAULT 0.00,
            `total_reviews` int(11) DEFAULT 0,
            `total_vehicle_count` int(11) DEFAULT 0,
            `created_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $pdo->exec($createTableSQL);
        echo "✓ vehicles table created successfully with all required columns!\n";
    } else {
        echo "✓ vehicles table exists\n";
        
        // Get current table structure
        echo "Checking current table structure...\n";
        $stmt = $pdo->query("DESCRIBE vehicles");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $columnNames = array_column($columns, 'Field');
        
        echo "Current columns: " . implode(', ', $columnNames) . "\n\n";
        
        // Define all required columns based on the rental module migrations
        $requiredColumns = [
            'day_wise_price' => 'DECIMAL(23,8) DEFAULT 0.00000000',
            'trip_day_wise' => 'DOUBLE DEFAULT 0',
            'hourly_price' => 'DECIMAL(23,8) DEFAULT 0.00000000',
            'distance_price' => 'DECIMAL(23,8) DEFAULT 0.00000000',
            'provider_id' => 'BIGINT(20) UNSIGNED DEFAULT NULL',
            'brand_id' => 'BIGINT(20) UNSIGNED DEFAULT NULL',
            'zone_id' => 'BIGINT(20) UNSIGNED DEFAULT NULL',
            'trip_hourly' => 'TINYINT(1) DEFAULT 0',
            'trip_distance' => 'TINYINT(1) DEFAULT 0',
            'multiple_vehicles' => 'TINYINT(1) DEFAULT 0',
            'air_condition' => 'TINYINT(1) DEFAULT 0',
            'new_tag' => 'TINYINT(1) DEFAULT 1',
            'total_trip' => 'INT(11) DEFAULT 0',
            'avg_rating' => 'DECIMAL(8,2) DEFAULT 0.00',
            'total_reviews' => 'INT(11) DEFAULT 0',
            'total_vehicle_count' => 'INT(11) DEFAULT 0',
            'description' => 'TEXT DEFAULT NULL',
            'thumbnail' => 'VARCHAR(255) DEFAULT NULL',
            'images' => 'TEXT DEFAULT NULL',
            'model' => 'VARCHAR(255) DEFAULT NULL',
            'engine_capacity' => 'VARCHAR(255) DEFAULT NULL',
            'engine_power' => 'VARCHAR(255) DEFAULT NULL',
            'seating_capacity' => 'VARCHAR(255) DEFAULT NULL',
            'fuel_type' => 'VARCHAR(255) DEFAULT NULL',
            'transmission_type' => 'VARCHAR(255) DEFAULT NULL',
            'discount_type' => 'VARCHAR(255) DEFAULT NULL',
            'discount_price' => 'DECIMAL(23,8) DEFAULT 0.00000000',
            'tag' => 'TEXT DEFAULT NULL',
            'documents' => 'TEXT DEFAULT NULL'
        ];
        
        // Add missing columns
        $addedColumns = 0;
        foreach ($requiredColumns as $column => $definition) {
            if (!in_array($column, $columnNames)) {
                echo "Adding missing column: $column\n";
                try {
                    $alterSQL = "ALTER TABLE `vehicles` ADD COLUMN `$column` $definition";
                    $pdo->exec($alterSQL);
                    echo "✓ $column column added successfully\n";
                    $addedColumns++;
                } catch (Exception $e) {
                    echo "✗ Failed to add $column: " . $e->getMessage() . "\n";
                }
            }
        }
        
        if ($addedColumns == 0) {
            echo "✓ All required columns already exist\n";
        } else {
            echo "✓ Added $addedColumns missing columns\n";
        }
        
        // Special check for day_wise_price column with correct data type
        echo "\nChecking day_wise_price column precision...\n";
        $stmt = $pdo->query("SHOW COLUMNS FROM vehicles LIKE 'day_wise_price'");
        $dayWiseColumn = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($dayWiseColumn) {
            echo "✓ day_wise_price column exists with type: " . $dayWiseColumn['Type'] . "\n";
            
            // Check if it has the correct precision
            if (strpos($dayWiseColumn['Type'], 'decimal(23,8)') === false) {
                echo "Updating day_wise_price column to correct precision...\n";
                try {
                    $alterSQL = "ALTER TABLE `vehicles` MODIFY COLUMN `day_wise_price` DECIMAL(23,8) DEFAULT 0.00000000";
                    $pdo->exec($alterSQL);
                    echo "✓ day_wise_price column updated to correct precision\n";
                } catch (Exception $e) {
                    echo "✗ Failed to update day_wise_price precision: " . $e->getMessage() . "\n";
                }
            } else {
                echo "✓ day_wise_price column has correct precision\n";
            }
        } else {
            echo "✗ day_wise_price column still missing after adding!\n";
        }
    }
    
    echo "\n=== TESTING THE PROBLEMATIC QUERY ===\n";
    try {
        $stmt = $pdo->query("SELECT MIN(day_wise_price) as aggregate FROM vehicles WHERE day_wise_price > 0");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "✓ Query executed successfully! Result: " . ($result['aggregate'] ?? 'NULL') . "\n";
        
        // Also test the other price columns
        $stmt = $pdo->query("SELECT MIN(hourly_price) as hourly_min, MIN(distance_price) as distance_min FROM vehicles WHERE hourly_price > 0 OR distance_price > 0");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "✓ Other price queries also work: hourly_min=" . ($result['hourly_min'] ?? 'NULL') . ", distance_min=" . ($result['distance_min'] ?? 'NULL') . "\n";
        
    } catch (Exception $e) {
        echo "✗ Query still failing: " . $e->getMessage() . "\n";
        echo "This means there might be another issue...\n";
    }
    
    echo "\n=== FINAL TABLE STRUCTURE ===\n";
    $stmt = $pdo->query("DESCRIBE vehicles");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($columns as $column) {
        echo sprintf("%-25s %-30s %s\n", 
            $column['Field'], 
            $column['Type'], 
            $column['Null'] == 'YES' ? 'NULL' : 'NOT NULL'
        );
    }
    
    echo "\n✓ Vehicles table structure fix completed!\n";
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
